#!/usr/bin/env bash

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"

ENV_FILE="${PROJECT_ROOT}/.azureenv"
if [[ -f "${ENV_FILE}" ]]; then
  set -a
  # shellcheck source=/dev/null
  source "${ENV_FILE}"
  set +a
fi

SUBSCRIPTION="${SUBSCRIPTION:-LSI<PERSON> Jarvis}"
RESOURCEGROUP="${RESOURCEGROUP:-appsvc_linux_centralindia_basic}"
SITENAME="${SITENAME:-Jarvisdev}"
SETTINGS_FILE="${1:-${PROJECT_ROOT}/.azure/appsettings.local.json}"

function require_cli() {
  if ! command -v "$1" >/dev/null 2>&1; then
    echo "Missing required CLI: $1" >&2
    exit 1
  fi
}

function ensure_login() {
  if ! az account show >/dev/null 2>&1; then
    echo "Azure CLI is not logged in. Run 'az login' first." >&2
    exit 1
  fi
}

if [[ ! -f "${SETTINGS_FILE}" ]]; then
  echo "Settings file not found: ${SETTINGS_FILE}" >&2
  echo "Copy .azure/appsettings.template.json to .azure/appsettings.local.json and fill in the values." >&2
  exit 1
fi

require_cli az
ensure_login

echo "Using subscription: ${SUBSCRIPTION}"
az account set --subscription "${SUBSCRIPTION}"

echo "Applying app settings from ${SETTINGS_FILE}"
az webapp config appsettings set \
  --name "${SITENAME}" \
  --resource-group "${RESOURCEGROUP}" \
  --settings @"${SETTINGS_FILE}"
