#!/usr/bin/env bash

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"

ENV_FILE="${PROJECT_ROOT}/.azureenv"
if [[ -f "${ENV_FILE}" ]]; then
  set -a
  # shellcheck source=/dev/null
  source "${ENV_FILE}"
  set +a
fi

SUBSCRIPTION="${SUBSCRIPTION:-LSI<PERSON> Jarvis}"
RESOURCEGROUP="${RESOURCEGROUP:-appsvc_linux_centralindia_basic}"
LOCATION="${LOCATION:-centralindia}"
PLANNAME="${PLANNAME:-ASP-appsvclinuxcentralindiabasic-9dd2}"
PLANSKU="${PLANSKU:-P1v2}"
SITENAME="${SITENAME:-Jarvisdev}"
RUNTIME="${RUNTIME:-NODE|22-lts}"
STARTUP_FILE="${STARTUP_FILE:-npm run start:prod}"
ENABLE_APP_LOGGING="${ENABLE_APP_LOGGING:-true}"
APP_LOG_LEVEL="${APP_LOG_LEVEL:-information}"
LOG_RETENTION_DAYS="${LOG_RETENTION_DAYS:-7}"
LOG_RETENTION_SUPPORTED=""

function require_cli() {
  if ! command -v "$1" >/dev/null 2>&1; then
    echo "Missing required CLI: $1" >&2
    exit 1
  fi
}

function ensure_login() {
  if ! az account show >/dev/null 2>&1; then
    echo "Azure CLI is not logged in. Run 'az login' first." >&2
    exit 1
  fi
}

function detect_retention_support() {
  if [[ -n "${LOG_RETENTION_SUPPORTED}" ]]; then
    return 0
  fi

  if az webapp log config --help | grep -q -- '--retention-days'; then
    LOG_RETENTION_SUPPORTED="true"
  else
    LOG_RETENTION_SUPPORTED="false"
  fi
}

function configure_logging() {
  local log_args=(
    --name "${SITENAME}"
    --resource-group "${RESOURCEGROUP}"
    --application-logging filesystem
    --level "${APP_LOG_LEVEL}"
    --web-server-logging filesystem
  )

  if [[ -n "${LOG_RETENTION_DAYS:-}" ]]; then
    detect_retention_support
    if [[ "${LOG_RETENTION_SUPPORTED}" == "true" ]]; then
      log_args+=(--retention-days "${LOG_RETENTION_DAYS}")
    else
      echo "Notice: azure-cli build does not support --retention-days; skipping retention configuration" >&2
    fi
  fi

  az webapp log config "${log_args[@]}" >/dev/null
}

require_cli az
ensure_login

echo "Using subscription: ${SUBSCRIPTION}"
az account set --subscription "${SUBSCRIPTION}"

if ! az group show --name "${RESOURCEGROUP}" >/dev/null 2>&1; then
  echo "Creating resource group ${RESOURCEGROUP} in ${LOCATION}"
  az group create --name "${RESOURCEGROUP}" --location "${LOCATION}" >/dev/null
else
  echo "Resource group ${RESOURCEGROUP} already exists"
fi

if ! az appservice plan show --name "${PLANNAME}" --resource-group "${RESOURCEGROUP}" >/dev/null 2>&1; then
  echo "Creating Linux App Service plan ${PLANNAME} (${PLANSKU})"
  az appservice plan create \
    --name "${PLANNAME}" \
    --resource-group "${RESOURCEGROUP}" \
    --location "${LOCATION}" \
    --sku "${PLANSKU}" \
    --is-linux >/dev/null
else
  echo "App Service plan ${PLANNAME} already exists"
fi

if ! az webapp show --name "${SITENAME}" --resource-group "${RESOURCEGROUP}" >/dev/null 2>&1; then
  echo "Creating Web App ${SITENAME}"
  az webapp create \
    --name "${SITENAME}" \
    --resource-group "${RESOURCEGROUP}" \
    --plan "${PLANNAME}" \
    --runtime "${RUNTIME}" \
    --deployment-local-git false >/dev/null
else
  echo "Web App ${SITENAME} already exists"
  echo "Ensuring runtime stack is set to ${RUNTIME}"
  az webapp config set \
    --name "${SITENAME}" \
    --resource-group "${RESOURCEGROUP}" \
    --linux-fx-version "${RUNTIME}" >/dev/null
fi

echo "Setting startup command to '${STARTUP_FILE}' and enforcing HTTPS/AlwaysOn"
az webapp config set \
  --name "${SITENAME}" \
  --resource-group "${RESOURCEGROUP}" \
  --startup-file "${STARTUP_FILE}" \
  --always-on true >/dev/null

az webapp update \
  --name "${SITENAME}" \
  --resource-group "${RESOURCEGROUP}" \
  --https-only true >/dev/null

if [[ "${ENABLE_APP_LOGGING}" == "true" ]]; then
  echo "Enabling filesystem logging (application/web)"
  configure_logging
else
  echo "Skipping log configuration (ENABLE_APP_LOGGING=false)"
fi

echo "Baseline Azure resources are ready."
