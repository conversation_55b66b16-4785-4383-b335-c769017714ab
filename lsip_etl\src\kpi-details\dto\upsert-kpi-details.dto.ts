import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsNumber,
  IsOptional,
  IsString,
  Length,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class KpiDetailRecordDto {
  @ApiProperty({ description: "Record ID", maxLength: 18 })
  @IsString()
  @Length(1, 18)
  id!: string;

  @ApiPropertyOptional({ description: "Port Co KPI Name", maxLength: 80 })
  @IsOptional()
  @IsString()
  @MaxLength(80)
  name?: string;

  @ApiPropertyOptional({ description: "Currency ISO Code", maxLength: 3 })
  @IsOptional()
  @IsString()
  @MaxLength(3)
  currencyIsoCode?: string;

  @ApiPropertyOptional({ description: "Company", maxLength: 18 })
  @IsOptional()
  @IsString()
  @MaxLength(18)
  companyId?: string;

  @ApiPropertyOptional({ description: "AOV (Rs.)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  aovRs?: number;

  @ApiPropertyOptional({ description: "Available?" })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  available?: boolean;

  @ApiPropertyOptional({ description: "Average Order Value Product" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  averageOrderValueProduct?: number;

  @ApiPropertyOptional({ description: "Average Order Value" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  averageOrderValue?: number;

  @ApiPropertyOptional({ description: "Business Unit", maxLength: 18 })
  @IsOptional()
  @IsString()
  @MaxLength(18)
  businessUnitId?: string;

  @ApiPropertyOptional({ description: "CAC (Rs.)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cacRs?: number;

  @ApiPropertyOptional({ description: "CAC" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cac?: number;

  @ApiPropertyOptional({ description: "CM2 (% of NMV)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cm2?: number;

  @ApiPropertyOptional({ description: "CM3 (% of NMV)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cm3OfNmv?: number;

  @ApiPropertyOptional({ description: "CM % in localities with >500 users" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cmInLocalitiesWith500Users?: number;

  @ApiPropertyOptional({ description: "Cumulative bank accounts" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cumulativeBankAccounts?: number;

  @ApiPropertyOptional({ description: "Bank accounts created per month" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  bankAccountsCreatedPerMonth?: number;

  @ApiPropertyOptional({ description: "Cash in Bank" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cashInBank?: number;

  @ApiPropertyOptional({ description: "Category", maxLength: 18 })
  @IsOptional()
  @IsString()
  @MaxLength(18)
  categoryId?: string;

  @ApiPropertyOptional({ description: "Contribution Margin Product" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  contributionMarginProduct?: number;

  @ApiPropertyOptional({ description: "Average deposit / bank account" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  averageDepositBankAccount?: number;

  @ApiPropertyOptional({ description: "Contribution Margin" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  contributionMargin?: number;

  @ApiPropertyOptional({ description: "# of Credit Card issued" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfCreditCardIssued?: number;

  @ApiPropertyOptional({ description: "Customer Acquisiton Cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  customerAcquisitonCost?: number;

  @ApiPropertyOptional({ description: "DAU" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  dau?: number;

  @ApiPropertyOptional({ description: "DTR from source" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  dtrFromSource?: number;

  @ApiPropertyOptional({ description: "Date" })
  @IsOptional()
  @IsDateString()
  date?: string;

  @ApiPropertyOptional({ description: "Cash Burn (Rs. Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cashBurnRsCr?: number;

  @ApiPropertyOptional({ description: "Cash Burn" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cashBurn?: number;

  @ApiPropertyOptional({ description: "EBITDA (Rs.Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ebitdaRsCr?: number;

  @ApiPropertyOptional({ description: "EBITDA" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ebitda?: number;

  @ApiPropertyOptional({ description: "Enable?" })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  enable?: boolean;

  @ApiPropertyOptional({ description: "First Party Mix (%)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  firstPartyMix?: number;

  @ApiPropertyOptional({ description: "First_Party" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  firstParty?: number;

  @ApiPropertyOptional({ description: "Fulfillment cost (% of NMV)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  fulfillmentCostOfNmv?: number;

  @ApiPropertyOptional({ description: "GMV" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gmv?: number;

  @ApiPropertyOptional({ description: "GMV from private labels" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gmvFromPrivateLabels?: number;

  @ApiPropertyOptional({ description: "GMV per EXISTING buyer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gmvPerExistingBuyer?: number;

  @ApiPropertyOptional({ description: "GMV per FOS cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gmvPerFosCost?: number;

  @ApiPropertyOptional({ description: "GMV per NEW buyer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gmvPerNewBuyer?: number;

  @ApiPropertyOptional({ description: "Contribution Margin (Rs. Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  contributionMarginRsCr?: number;

  @ApiPropertyOptional({ description: "Gross Margin" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  grossMargin?: number;

  @ApiPropertyOptional({ description: "Gross Margin (% of NMV)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  grossMarginOfNmv?: number;

  @ApiPropertyOptional({ description: "Inventory days" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  inventoryDays?: number;

  @ApiPropertyOptional({ description: "Latest Flag" })
  @IsOptional()
  @Type(() => Boolean)
  @IsBoolean()
  latestFlag?: boolean;

  @ApiPropertyOptional({ description: "M12 GMV retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m12GmvRetention?: number;

  @ApiPropertyOptional({ description: "M12 user retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m12UserRetention?: number;

  @ApiPropertyOptional({ description: "M1 Repeat (%)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m1Repeat?: number;

  @ApiPropertyOptional({ description: "M2 GMV retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m2GmvRetention?: number;

  @ApiPropertyOptional({ description: "Contribution Margin (% of take rate)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  contributionMarginOfTakeRate?: number;

  @ApiPropertyOptional({ description: "M2 user retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m2UserRetention?: number;

  @ApiPropertyOptional({ description: "M6 GMV retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m6GmvRetention?: number;

  @ApiPropertyOptional({ description: "M6 user retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m6UserRetention?: number;

  @ApiPropertyOptional({ description: "Discretionary marketing" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  discretionaryMarketing?: number;

  @ApiPropertyOptional({ description: "Gross Margin Product" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  grossMarginProduct?: number;

  @ApiPropertyOptional({ description: "MAU" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  mau?: number;

  @ApiPropertyOptional({ description: "Marketing Spends" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketingSpends?: number;

  @ApiPropertyOptional({ description: "M2 retention (%)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m2Retention?: number;

  @ApiPropertyOptional({ description: "Marketplace float" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketplaceFloat?: number;

  @ApiPropertyOptional({ description: "Merchant Acquisition Cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  merchantAcquisitionCost?: number;

  @ApiPropertyOptional({ description: "NMV(Rs.Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  nmv?: number;

  @ApiPropertyOptional({ description: "NMV per buyer (Rs.)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  nmvPerBuyerRs?: number;

  @ApiPropertyOptional({ description: "No of active trucks" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfActiveTrucks?: number;

  @ApiPropertyOptional({ description: "No of dense lanes" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfDenseLanes?: number;

  @ApiPropertyOptional({ description: "No of dense localities" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfDenseLocalities?: number;

  @ApiPropertyOptional({ description: "No of orders" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfOrders?: number;

  @ApiPropertyOptional({ description: "No of paying merchants" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfPayingMerchants?: number;

  @ApiPropertyOptional({ description: "No of tracked trips" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfTrackedTrips?: number;

  @ApiPropertyOptional({ description: "No of transacting buyers" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfTransactingBuyers?: number;

  @ApiPropertyOptional({ description: "No of transacting users" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfTransactingUsers?: number;

  @ApiPropertyOptional({ description: "No of transactions" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfTransactions?: number;

  @ApiPropertyOptional({ description: "MAC (Rs.)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  macRs?: number;

  @ApiPropertyOptional({ description: "Number of buyers (number)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfBuyersNumber?: number;

  @ApiPropertyOptional({ description: "Number of localities with >500 users" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfLocalitiesWith500Users?: number;

  @ApiPropertyOptional({ description: "Number of new users ('000)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfNewUsers?: number;

  @ApiPropertyOptional({ description: "Number of transacting users (Lakh)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfTransactingUsersLakh?: number;

  @ApiPropertyOptional({ description: "Operating Cash Burn (Rs.Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  operatingCashBurnRsCr?: number;

  @ApiPropertyOptional({ description: "Order Frequency" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  orderFrequency?: number;

  @ApiPropertyOptional({ description: "Payable days" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  payableDays?: number;

  @ApiPropertyOptional({ description: "Paying Merchants (Lakhs)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  payingMerchants?: number;

  @ApiPropertyOptional({ description: "People + G&A + Others (Rs. Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  peopleGAOthersRsCr?: number;

  @ApiPropertyOptional({ description: "MAU (Lakh)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  mauLakh?: number;

  @ApiPropertyOptional({ description: "Marketing" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketing?: number;

  @ApiPropertyOptional({ description: "Receivable days" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  receivableDays?: number;

  @ApiPropertyOptional({ description: "Record Type Name", maxLength: 1300 })
  @IsOptional()
  @IsString()
  @MaxLength(1300)
  recordTypeName?: string;

  @ApiPropertyOptional({ description: "RevenueTotal" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  revenuetotal?: number;

  @ApiPropertyOptional({ description: "Sales Cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  salesCost?: number;

  @ApiPropertyOptional({ description: "Shipping cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  shippingCost?: number;

  @ApiPropertyOptional({ description: "Shipping cost (% of NMV)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  shippingCostOfNmv?: number;

  @ApiPropertyOptional({ description: "Take Rate GMV" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateGmv?: number;

  @ApiPropertyOptional({ description: "Take Rate (Rs. Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateRsCr?: number;

  @ApiPropertyOptional({ description: "Take Rate" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRate?: number;

  @ApiPropertyOptional({ description: "Take Rate by Revenue Product" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateByRevenueProduct?: number;

  @ApiPropertyOptional({ description: "Take Rate by Revenue" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateByRevenue?: number;

  @ApiPropertyOptional({ description: "Takerate in localities with >500users(%)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateFromLocalitiesWith500User?: number;

  @ApiPropertyOptional({ description: "Take rate per user (Rs.)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRatePerUserRs?: number;

  @ApiPropertyOptional({ description: "Take rate revenue from dense localitie" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateRevenueFromDenseLocalitie?: number;

  @ApiPropertyOptional({ description: "Take rate revenue per existing user" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateRevenuePerExistingUser?: number;

  @ApiPropertyOptional({ description: "Take rate revenue per new user" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateRevenuePerNewUser?: number;

  @ApiPropertyOptional({ description: "Test KPI" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  testKpi?: number;

  @ApiPropertyOptional({ description: "Total People Cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalPeopleCost?: number;

  @ApiPropertyOptional({ description: "Transacting Buyers" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  transactingBuyers?: number;

  @ApiPropertyOptional({ description: "Transacting stores per FOS" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  transactingStoresPerFos?: number;

  @ApiPropertyOptional({ description: "Transacting user in localities with >500" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  transactingUserInLocalitiesWith500?: number;

  @ApiPropertyOptional({ description: "Type of KPI", maxLength: 255 })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  typeOfKpi?: string;

  @ApiPropertyOptional({ description: "Working Capital" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  workingCapital?: number;

  @ApiPropertyOptional({ description: "Key", maxLength: 100 })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  key?: string;

  @ApiPropertyOptional({ description: "Number of buyers (number)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfBuyers?: number;

  @ApiPropertyOptional({ description: "Revenue(Rs.Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  revenueRsCr?: number;

  @ApiPropertyOptional({ description: "CM1 (% of NMV)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cm1?: number;

  @ApiPropertyOptional({ description: "Customer Support & Biz Ops Cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  customerSupportBizOpsCost?: number;

  @ApiPropertyOptional({ description: "Under-utilized fixed costs in operations" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  underUtilizedFixedCostsInOperations?: number;

  @ApiPropertyOptional({ description: "Tech & Product Costs" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  techProductCosts?: number;

  @ApiPropertyOptional({ description: "SG&A" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  sgA?: number;

  @ApiPropertyOptional({ description: "% buyer with credit" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  buyerWithCredit?: number;

  @ApiPropertyOptional({ description: "% YIM (12 months after DPD)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  yim12MonthsAfterDpd?: number;

  @ApiPropertyOptional({ description: "Performance marketing" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  performanceMarketing?: number;

  @ApiPropertyOptional({ description: "CM (Rs. Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cmRsCr?: number;

  @ApiPropertyOptional({ description: "takeRate of localities> 500 users(RS))" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateOfLocalitiesWith500Users?: number;

  @ApiPropertyOptional({ description: "Performance marketing (Rs. Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  performanceMarketingRsCr?: number;

  @ApiPropertyOptional({ description: "CM % in localities >500 users(Rs. cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cmInLocalities500UsersRsCr?: number;

  @ApiPropertyOptional({ description: "Paying users" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  payingUsers?: number;

  @ApiPropertyOptional({ description: "m3 paying user retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m3PayingUserRetention?: number;

  @ApiPropertyOptional({ description: "m3 paying revenue retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m3PayingRevenueRetention?: number;

  @ApiPropertyOptional({ description: "# of audio heard per DAU" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfAudioHeardPerDau?: number;

  @ApiPropertyOptional({ description: "MO ARPU" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  moArpu?: number;

  @ApiPropertyOptional({ description: "Total ARPU" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalArpu?: number;

  @ApiPropertyOptional({ description: "CAC ( $)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cacDollar?: number;

  @ApiPropertyOptional({ description: "M0 Conversion (%)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m0Conversion?: number;

  @ApiPropertyOptional({ description: "Conversion (%)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  conversion?: number;

  @ApiPropertyOptional({ description: "Subscritpion income ($)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  subscritpionIncome?: number;

  @ApiPropertyOptional({ description: "Marketing cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketingCost?: number;

  @ApiPropertyOptional({ description: "Employee Expense (India + US)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  employeeExpenseIndiaUs?: number;

  @ApiPropertyOptional({ description: "EBITDA ( $ )" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ebitdaDollar?: number;

  @ApiPropertyOptional({ description: "Total Cashburn ( $)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalCashburn?: number;

  @ApiPropertyOptional({ description: "NMV (USD)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  nmvUsd?: number;

  @ApiPropertyOptional({ description: "FMCG_NMV _%" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  fmcgNmv?: number;

  @ApiPropertyOptional({ description: "Teman Ula NMV %" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  temanUlaNmv?: number;

  @ApiPropertyOptional({ description: "Others NMV %" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  othersNmv?: number;

  @ApiPropertyOptional({ description: "Take Rate Net Margin %" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateNetMargin?: number;

  @ApiPropertyOptional({ description: "Take Rate FMCG %" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateFmcg?: number;

  @ApiPropertyOptional({ description: "Take Rate Terman Ula %" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateTermanUla?: number;

  @ApiPropertyOptional({ description: "Logistics Cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  logisticsCost?: number;

  @ApiPropertyOptional({ description: "FoS" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  fos?: number;

  @ApiPropertyOptional({ description: "Warehouse" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  warehouse?: number;

  @ApiPropertyOptional({ description: "Cash in Bank ($)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cashInBankDollar?: number;

  @ApiPropertyOptional({ description: "Unique Ordering Stores" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  uniqueOrderingStores?: number;

  @ApiPropertyOptional({ description: "M6 Store retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m6StoreRetention?: number;

  @ApiPropertyOptional({ description: "NMV per store per month" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  nmvPerStorePerMonth?: number;

  @ApiPropertyOptional({ description: "AOV ($)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  aovDollar?: number;

  @ApiPropertyOptional({ description: "Orders per store per month" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ordersPerStorePerMonth?: number;

  @ApiPropertyOptional({ description: "App Running Cost (Server & Analytics )" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  appRunningCostServerAnalyticsCos?: number;

  @ApiPropertyOptional({ description: "Marketing Cost (%)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketingCostPercent?: number;

  @ApiPropertyOptional({ description: "Content Serving Cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  contentServingCost?: number;

  @ApiPropertyOptional({ description: "Other Branding Expenses" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  otherBrandingExpenses?: number;

  @ApiPropertyOptional({ description: "Music License Cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  musicLicenseCost?: number;

  @ApiPropertyOptional({ description: "Employee Benefits Expenses" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  employeeBenefitsExpenses?: number;

  @ApiPropertyOptional({ description: "Others" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  others?: number;

  @ApiPropertyOptional({ description: "DAU / MAU" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  dauMau?: number;

  @ApiPropertyOptional({ description: "Retention D30" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  retentionD30?: number;

  @ApiPropertyOptional({ description: "Average time spent" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  averageTimeSpent?: number;

  @ApiPropertyOptional({ description: "Unique Creators(mn)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  uniqueCreators?: number;

  @ApiPropertyOptional({ description: "Total Installs (mn)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalInstalls?: number;

  @ApiPropertyOptional({ description: "% Organic" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  organic?: number;

  @ApiPropertyOptional({ description: "Cost Per Install" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  costPerInstall?: number;

  @ApiPropertyOptional({ description: "Revenue" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  revenueDollar?: number;

  @ApiPropertyOptional({ description: "Skilling" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  skilling?: number;

  @ApiPropertyOptional({ description: "Recruitment" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  recruitment?: number;

  @ApiPropertyOptional({ description: "Marketing + Others" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketingOthers?: number;

  @ApiPropertyOptional({ description: "Salary" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  salary?: number;

  @ApiPropertyOptional({ description: "New Installs" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newInstalls?: number;

  @ApiPropertyOptional({ description: "Profile Complete" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  profileComplete?: number;

  @ApiPropertyOptional({ description: "Uninstall profile completes" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  uninstallProfileCompletes?: number;

  @ApiPropertyOptional({ description: "Screened Leads" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  screenedLeads?: number;

  @ApiPropertyOptional({ description: "From existing" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  fromExisting?: number;

  @ApiPropertyOptional({ description: "From reactivated" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  fromReactivated?: number;

  @ApiPropertyOptional({ description: "From New" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  fromNew?: number;

  @ApiPropertyOptional({ description: "Screened Leads / MAU" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  screenedLeadsMau?: number;

  @ApiPropertyOptional({ description: "M3 user retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m3UserRetention?: number;

  @ApiPropertyOptional({ description: "Repeat MAU to Life to date User Base" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  repeatMauToLifeToDateUserBase?: number;

  @ApiPropertyOptional({ description: "Blended CAC for user" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  blendedCacForUser?: number;

  @ApiPropertyOptional({ description: "New employer acquired" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newEmployerAcquired?: number;

  @ApiPropertyOptional({ description: "Active employer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  activeEmployer?: number;

  @ApiPropertyOptional({ description: "Jobs activated" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  jobsActivated?: number;

  @ApiPropertyOptional({ description: "M3 Employer retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m3EmployerRetention?: number;

  @ApiPropertyOptional({ description: "Repeat Employer to Life to date Employer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  repeatEmployerToLifeToDateEmployer?: number;

  @ApiPropertyOptional({ description: "Blended CAC Employer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  blendedCacEmployer?: number;

  @ApiPropertyOptional({ description: "% Community MAU of App MAU" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  communityMauOfAppMau?: number;

  @ApiPropertyOptional({ description: "Community to Community M1 (D30-D60) Rete" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  communityToCommunityM1D30D60Rete?: number;

  @ApiPropertyOptional({ description: "Overall Enquiries Received (MTD & YTD )" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  overallEnquiriesReceivedMtdYtd?: number;

  @ApiPropertyOptional({ description: "Conversion rate (trailing 3 months)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  conversionRateTrailing3Months?: number;

  @ApiPropertyOptional({ description: "New Orders for the month" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newOrdersForTheMonth?: number;

  @ApiPropertyOptional({ description: "GTV ARR ON SAAS" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gtvArrOnSaas?: number;

  @ApiPropertyOptional({ description: "GMV" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gmvDollar?: number;

  @ApiPropertyOptional({ description: "Take Rate" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateDollar?: number;

  @ApiPropertyOptional({ description: "Delivered GMV for the month" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  deliveredGmvForTheMonth?: number;

  @ApiPropertyOptional({ description: "Cumulative Open Order Book (Closing)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cumulativeOpenOrderBookClosing?: number;

  @ApiPropertyOptional({ description: "CM" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cmPercent?: number;

  @ApiPropertyOptional({ description: "Number of LSPs + Shippers added" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfLspsShippersAdded?: number;

  @ApiPropertyOptional({ description: "Active shippers + LSPs" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  activeShippersLsps?: number;

  @ApiPropertyOptional({ description: "Fixed Expenses" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  fixedExpenses?: number;

  @ApiPropertyOptional({ description: "GMV from Repeat Customers (%)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gmvFromRepeatCustomers?: number;

  @ApiPropertyOptional({ description: "New Customers acquired" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newCustomersAcquired?: number;

  @ApiPropertyOptional({ description: "Active Customers" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  activeCustomers?: number;

  @ApiPropertyOptional({ description: "Q3 customer retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  q3CustomerRetention?: number;

  @ApiPropertyOptional({ description: "Active Suppliers" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  activeSuppliers?: number;

  @ApiPropertyOptional({ description: "Q3 supplier retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  q3SupplierRetention?: number;

  @ApiPropertyOptional({ description: "AR: (Value in Rs Crs)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  arValueInRsCrs?: number;

  @ApiPropertyOptional({ description: "AP: (Value in Rs Crs)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  apValueInRsCrs?: number;

  @ApiPropertyOptional({ description: "Inventory: (Value in Rs Crs)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  inventoryValueInRsCrs?: number;

  @ApiPropertyOptional({ description: "Net WC (AR-AP+Inv+Adv)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  netWcArApInvAdv?: number;

  @ApiPropertyOptional({ description: "WC Conversion Days" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  wcConversionDays?: number;

  @ApiPropertyOptional({ description: "Free Cash" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  freeCash?: number;

  @ApiPropertyOptional({ description: "Ending CARR" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  endingCarr?: number;

  @ApiPropertyOptional({ description: "New CARR + Upsell" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newCarrUpsell?: number;

  @ApiPropertyOptional({ description: "Monthly Churn/Downgrade" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  monthlyChurnDowngrade?: number;

  @ApiPropertyOptional({ description: "No of customers (CARR)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfCustomersCarr?: number;

  @ApiPropertyOptional({ description: "CARR per customer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  carrPerCustomer?: number;

  @ApiPropertyOptional({ description: "Ending ARR" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  endingArr?: number;

  @ApiPropertyOptional({ description: "New ARR + Upsell" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newArrUpsell?: number;

  @ApiPropertyOptional({ description: "Monthly Churn/Downgrade(ARR)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  monthlyChurnDowngradeArr?: number;

  @ApiPropertyOptional({ description: "Number of customers (ARR)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfCustomersArr?: number;

  @ApiPropertyOptional({ description: "ARR per customer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  arrPerCustomer?: number;

  @ApiPropertyOptional({ description: "ARR as a % of CARR" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  arrAsAOfCarr?: number;

  @ApiPropertyOptional({ description: "Platform" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  platform?: number;

  @ApiPropertyOptional({ description: "CPaaS" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cpaas?: number;

  @ApiPropertyOptional({ description: "Voice" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  voice?: number;

  @ApiPropertyOptional({ description: "Services" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  services?: number;

  @ApiPropertyOptional({ description: "Gross Profit" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  grossProfit?: number;

  @ApiPropertyOptional({ description: "Operating Income" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  operatingIncome?: number;

  @ApiPropertyOptional({ description: "DSO" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  dso?: number;

  @ApiPropertyOptional({ description: "Net retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  netRetention?: number;

  @ApiPropertyOptional({ description: "Average CARR per new customer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  averageCarrPerNewCustomer?: number;

  @ApiPropertyOptional({ description: "CARR/FTE" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  carrFte?: number;

  @ApiPropertyOptional({ description: "ARR/FTE" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  arrFte?: number;

  @ApiPropertyOptional({ description: "Annualised net churn" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  annualisedNetChurn?: number;

  @ApiPropertyOptional({ description: "Rule of 40" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ruleOf40?: number;

  @ApiPropertyOptional({ description: "Net Magic Number" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  netMagicNumber?: number;

  @ApiPropertyOptional({ description: "Sales cost ($000)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  salesCost000?: number;

  @ApiPropertyOptional({ description: "Net Revenue" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  netRevenue?: number;

  @ApiPropertyOptional({ description: "Other revenue (e.g.shipping revenue etc)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  otherRevenueEGShippingRevenueEtc?: number;

  @ApiPropertyOptional({ description: "COGS" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cogs?: number;

  @ApiPropertyOptional({ description: "All discounts / refunds etc." })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  allDiscountsRefundsEtc?: number;

  @ApiPropertyOptional({ description: "Wastage" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  wastage?: number;

  @ApiPropertyOptional({ description: "Parking + Fuel (=last mile)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  parkingFuelLastMile?: number;

  @ApiPropertyOptional({ description: "Packaging cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  packagingCost?: number;

  @ApiPropertyOptional({ description: "Hub operations cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  hubOperationsCost?: number;

  @ApiPropertyOptional({ description: "CM2" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cm2PercentageOfNetrevenue?: number;

  @ApiPropertyOptional({ description: "Marketing cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketingCostPercentageOfNetrevenue?: number;

  @ApiPropertyOptional({ description: "CM3" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cm3PercentOfNetRevenue?: number;

  @ApiPropertyOptional({ description: "# of orders" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfOrders?: number;

  @ApiPropertyOptional({ description: "Existing buyers" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  existingBuyers?: number;

  @ApiPropertyOptional({ description: "Orders per buyer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ordersPerBuyer?: number;

  @ApiPropertyOptional({ description: "Existing buyers (No of buyer per month)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  existingBuyersNoBuyerPermonth?: number;

  @ApiPropertyOptional({ description: "# of unique buyers" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  noOfUniqueBuyers?: number;

  @ApiPropertyOptional({ description: "Existing buyers(#'000)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  existingBuyers000?: number;

  @ApiPropertyOptional({ description: "% GMV from existing buyers" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gmvFromExistingBuyers?: number;

  @ApiPropertyOptional({ description: "M3 GMV retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m3GmvRetention?: number;

  @ApiPropertyOptional({ description: "% GMV from top 3 categories" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gmvFromTop3Categories?: number;

  @ApiPropertyOptional({ description: "Gross Booking" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  grossBooking?: number;

  @ApiPropertyOptional({ description: "New + Expansion" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newExpansion?: number;

  @ApiPropertyOptional({ description: "Downgrade + Churn" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  downgradeChurn?: number;

  @ApiPropertyOptional({ description: "NDR" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ndr?: number;

  @ApiPropertyOptional({ description: "(-) Sales & Marketing" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  salesMarketing?: number;

  @ApiPropertyOptional({ description: "(-) General & Administrative" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  generalAdministrative?: number;

  @ApiPropertyOptional({ description: "(-) Research & Development" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  researchDevelopment?: number;

  @ApiPropertyOptional({ description: "Net Burn $000" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  netBurn000?: number;

  @ApiPropertyOptional({ description: "Burn multiple" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  burnMultiple?: number;

  @ApiPropertyOptional({ description: "Avg. new deal size" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  avgNewDealSize?: number;

  @ApiPropertyOptional({ description: "Event Created" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  eventCreated?: number;

  @ApiPropertyOptional({ description: "Event Hosted" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  eventHosted?: number;

  @ApiPropertyOptional({ description: "% events with <500 participants" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  eventsWithLessThan500Participants?: number;

  @ApiPropertyOptional({ description: "% events with 500-1000 participants" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  eventsWith500To1000Participants?: number;

  @ApiPropertyOptional({ description: "% events with >1000 participants" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  eventsWithGreater1000Participants?: number;

  @ApiPropertyOptional({ description: "ROI on marketing spends" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  roiOnMarketingSpends?: number;

  @ApiPropertyOptional({ description: "ROI on sales spends" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  roiOnSalesSpends?: number;

  @ApiPropertyOptional({ description: "Sales Quota Attainment" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  salesQuotaAttainment?: number;

  @ApiPropertyOptional({ description: "People Related Spend" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  peopleRelatedSpend?: number;

  @ApiPropertyOptional({ description: "Cash Burn ($000)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cashBurnDollar000?: number;

  @ApiPropertyOptional({ description: "Net dollar retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  netDollarRetention?: number;

  @ApiPropertyOptional({ description: "Gross $Retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  grossRetention?: number;

  @ApiPropertyOptional({ description: "Net seat Retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  netSeatRetention?: number;

  @ApiPropertyOptional({ description: "Gross Seat Retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  grossSeatRetention?: number;

  @ApiPropertyOptional({ description: "Logo Retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  logoRetention?: number;

  @ApiPropertyOptional({ description: "Total S&M Spend" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalSMSpend?: number;

  @ApiPropertyOptional({ description: "S&M Efficiency" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  sMEfficiency?: number;

  @ApiPropertyOptional({ description: "New Logo ARR" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newLogoArr?: number;

  @ApiPropertyOptional({ description: "New Logo Seats" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newLogoSeats?: number;

  @ApiPropertyOptional({ description: "New Logo" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newLogo?: number;

  @ApiPropertyOptional({ description: "New Logo ASP" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newLogoAsp?: number;

  @ApiPropertyOptional({ description: "New Logo Avg Seats" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newLogoAvgSeats?: number;

  @ApiPropertyOptional({ description: "EBITDA burn" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ebitdaBurn?: number;

  @ApiPropertyOptional({ description: "Total bookings" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalBookings?: number;

  @ApiPropertyOptional({ description: "Global" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  global?: number;

  @ApiPropertyOptional({ description: "SMB" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  smb?: number;

  @ApiPropertyOptional({ description: "MM" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  mm?: number;

  @ApiPropertyOptional({ description: "Enterprise" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  enterprise?: number;

  @ApiPropertyOptional({ description: "Invoiced Revenue" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  invoicedRevenue?: number;

  @ApiPropertyOptional({ description: "GM" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gm?: number;

  @ApiPropertyOptional({ description: "SMB" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  smbPercent?: number;

  @ApiPropertyOptional({ description: "MM" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  mmPercent?: number;

  @ApiPropertyOptional({ description: "Enterprise" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  enterprisePercent?: number;

  @ApiPropertyOptional({ description: "ML to MQL" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  mlToMql?: number;

  @ApiPropertyOptional({ description: "MQL to SQL" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  mqlToSql?: number;

  @ApiPropertyOptional({ description: "SQL to Closed" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  sqlToClosed?: number;

  @ApiPropertyOptional({ description: "Gross ROI" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  grossRoi?: number;

  @ApiPropertyOptional({ description: "Outbound quota attainment" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  outboundQuotaAttainment?: number;

  @ApiPropertyOptional({ description: "Enterprise + MM CAC" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  enterpriseMmCac?: number;

  @ApiPropertyOptional({ description: "SMB inbound+marketing CAC" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  smbInboundMarketingCac?: number;

  @ApiPropertyOptional({ description: "SAAS - logo Q1 retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  saasLogoQ1Retention?: number;

  @ApiPropertyOptional({ description: "SAAS - revenue Q1 retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  saasRevenueQ1Retention?: number;

  @ApiPropertyOptional({ description: "SAAS - conversion, self serve funnel" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  saasConversionSelfServeFunnel?: number;

  @ApiPropertyOptional({ description: "Marketplace - Enterprise - logo Q1 reten" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketplaceEnterpriseLogoQ1Reten?: number;

  @ApiPropertyOptional({ description: "Marketplace - Enterprise - revenue Q1 re" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketplaceEnterpriseRevenueQ1Re?: number;

  @ApiPropertyOptional({ description: "Marketplace - MM - logo Q1 retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketplaceMmLogoQ1Retention?: number;

  @ApiPropertyOptional({ description: "Marketplace - MM - revenue Q1 retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  marketplaceMmRevenueQ1Retention?: number;

  @ApiPropertyOptional({ description: "Creator Earnings" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  creatorEarnings?: number;

  @ApiPropertyOptional({ description: "Editor Earnings" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  editorEarnings?: number;

  @ApiPropertyOptional({ description: "CM1" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cm1Percent?: number;

  @ApiPropertyOptional({ description: "Inbound quota attainment" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  inboundQuotaAttainment?: number;

  @ApiPropertyOptional({ description: "M3 Spend per customer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m3SpendPerCustomer?: number;

  @ApiPropertyOptional({ description: "M3 / M0 spend per customer" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m3M0SpendPerCustomer?: number;

  @ApiPropertyOptional({ description: "NPAs" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  npas?: number;

  @ApiPropertyOptional({ description: "Money remitted to India on Zolve" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  moneyRemittedToIndiaOnZolve?: number;

  @ApiPropertyOptional({ description: "Discretionary marketing (Rs. Cr)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  discretionaryMarketingRsCr?: number;

  @ApiPropertyOptional({ description: "Take Rate Others %" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  takeRateOthers?: number;

  @ApiPropertyOptional({ description: "Subs Revenue" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  subsRevenue?: number;

  @ApiPropertyOptional({ description: "Subs - Hosting Cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  subsHostingCost?: number;

  @ApiPropertyOptional({ description: "Subs - Client Support Cost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  subsClientSupportCost?: number;

  @ApiPropertyOptional({ description: "ARR/CARR" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  arrCarr?: number;

  @ApiPropertyOptional({ description: "IARR (contracted)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  iarrContracted?: number;

  @ApiPropertyOptional({ description: "Employees on platform (Contracted)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  employeesOnPlatformContracted?: number;

  @ApiPropertyOptional({ description: "Average ACV" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  averageAcv?: number;

  @ApiPropertyOptional({ description: "Trading volume" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  tradingVolume?: number;

  @ApiPropertyOptional({ description: "Trading Revenue" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  tradingRevenue?: number;

  @ApiPropertyOptional({ description: "Cumulative downloads" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cumulativeDownloads?: number;

  @ApiPropertyOptional({ description: "Cumulative KYCed users" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cumulativeKycedUsers?: number;

  @ApiPropertyOptional({ description: "# of active traders" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfActiveTraders?: number;

  @ApiPropertyOptional({ description: "Trader W24 retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  traderW24Retention?: number;

  @ApiPropertyOptional({ description: "Average Trading Frequency per Trader" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  averageTradingFrequencyPerTrader?: number;

  @ApiPropertyOptional({ description: "Total Contracts Value (ARR)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalContractsValueArr?: number;

  @ApiPropertyOptional({ description: "Terminal ARR" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalTerminalArr?: number;

  @ApiPropertyOptional({ description: "Terminal ARR net of discounts" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  terminalArrNetOfDiscounts?: number;

  @ApiPropertyOptional({ description: "Terminal ARR added" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  terminalArrAdded?: number;

  @ApiPropertyOptional({ description: "Terminal ARR lost" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  terminalArrLost?: number;

  @ApiPropertyOptional({ description: "Non recurring revenue" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  nonRecurringRevenue?: number;

  @ApiPropertyOptional({ description: "Total institutes" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalInstitutes?: number;

  @ApiPropertyOptional({ description: "# of new institutes" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfNewInstitutes?: number;

  @ApiPropertyOptional({ description: "Terminal ARR / institute" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  terminalArrInstitute?: number;

  @ApiPropertyOptional({ description: "S&M efficiency net of discounts" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  sMEfficiencyNetOfDiscounts?: number;

  @ApiPropertyOptional({ description: "Total creators" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalCreators?: number;

  @ApiPropertyOptional({ description: "# of new creators" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfNewCreators?: number;

  @ApiPropertyOptional({ description: "Total customers" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  totalCustomers?: number;

  @ApiPropertyOptional({ description: "Terminal ARR lost($)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  terminalArrLostDollar?: number;

  @ApiPropertyOptional({ description: "Q2 trip retention" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  q2TripRetention?: number;

  @ApiPropertyOptional({ description: "M6 LSP reten. adjusted for pilot / trail" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m6LspRetenAdjustedForPilotTrail?: number;

  @ApiPropertyOptional({ description: "M6 Shipper retention adjusted / trials" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m6ShipperRetentionAdjustedTrials?: number;

  @ApiPropertyOptional({ description: "Number of suppliers added" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  numberOfSuppliersAdded?: number;

  @ApiPropertyOptional({ description: "AUM Total" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  aumTotal?: number;

  @ApiPropertyOptional({ description: "Gross Profit" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  grossProfitPercent?: number;

  @ApiPropertyOptional({ description: "AUM Cash" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  aumCash?: number;

  @ApiPropertyOptional({ description: "AUM CPF" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  aumCpf?: number;

  @ApiPropertyOptional({ description: "AUM SRS" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  aumSrs?: number;

  @ApiPropertyOptional({ description: "New investment" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newInvestment?: number;

  @ApiPropertyOptional({ description: "Redemption" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  redemption?: number;

  @ApiPropertyOptional({ description: "Net new money invested" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  netNewMoneyInvested?: number;

  @ApiPropertyOptional({ description: "Existing Clients" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  existingClients?: number;

  @ApiPropertyOptional({ description: "New Clients" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  newClients?: number;

  @ApiPropertyOptional({ description: "Registered accounts" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  registeredAccounts?: number;

  @ApiPropertyOptional({ description: "Broker accounts" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  brokerAccounts?: number;

  @ApiPropertyOptional({ description: "Funded clients" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  fundedClients?: number;

  @ApiPropertyOptional({ description: "Churned clients (from company)" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  churnedClientsFromCompany?: number;

  @ApiPropertyOptional({ description: "Share of peer referrals" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  shareOfPeerReferrals?: number;

  @ApiPropertyOptional({ description: "Recurring amount" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  recurringAmount?: number;

  @ApiPropertyOptional({ description: "GM / client" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  gmClient?: number;

  @ApiPropertyOptional({ description: "Payback period on CAC" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  paybackPeriodOnCac?: number;

  @ApiPropertyOptional({ description: "M6 median net invested" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  m6MedianNetInvested?: number;

  @ApiPropertyOptional({ description: "cm" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  cmPercentRevenue?: number;

  @ApiPropertyOptional({ description: "Expansion ARR" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  expansionArr?: number;

  @ApiPropertyOptional({ description: "Expansion ARR/Overall ARR" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  expansionArrOverallArr?: number;

  @ApiPropertyOptional({ description: "Headcount" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  headcount?: number;

  @ApiPropertyOptional({ description: "Gross Margin" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  grossMarginDollar?: number;

  @ApiPropertyOptional({ description: "Income" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  income?: number;

  @ApiPropertyOptional({ description: "ARR:CAC" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  arrCac?: number;

  @ApiPropertyOptional({ description: "Churn %" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  churn?: number;

  @ApiPropertyOptional({ description: "LTV:CAC" })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  ltvCac?: number;
}

export class UpsertKpiDetailsDto {
  @ApiProperty({ type: () => [KpiDetailRecordDto] })
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => KpiDetailRecordDto)
  KpiDetails!: KpiDetailRecordDto[];
}

export type KpiDetailRecord = KpiDetailRecordDto;
