import { SwaggerModule } from '@nestjs/swagger';
import { SwaggerConfig, sanitizeSwaggerDocument, swaggerDocPath, swaggerJsonPath } from './swagger.config';

describe('sanitizeSwaggerDocument', () => {
  it('replaces line and paragraph separators to keep generated JS valid', () => {
    const document = {
      info: {
        title: 'Test',
        description: 'Line\u2028break and paragraph\u2029separator',
      },
      tags: [{ description: 'Another\u2028line' }],
    } as any;

    const sanitized = sanitizeSwaggerDocument(document);

    expect(sanitized.info.description).toBe('Line break and paragraph separator');
    expect(sanitized.tags?.[0]?.description).toBe('Another line');
  });
});

describe('SwaggerConfig.setup', () => {
  const mockApp = {} as any;
  const createDocumentSpy = jest.spyOn(SwaggerModule, 'createDocument');
  const setupSpy = jest.spyOn(SwaggerModule, 'setup');

  beforeEach(() => {
    createDocumentSpy.mockReset();
    setupSpy.mockReset();
  });

  it('creates and registers the swagger document with sanitisation', () => {
    const swaggerDocument: any = {
      info: { description: 'With\u2028invalid separator' },
    };
    createDocumentSpy.mockReturnValue(swaggerDocument);

    SwaggerConfig.setup(mockApp);

    expect(createDocumentSpy).toHaveBeenCalledWith(
      mockApp,
      expect.objectContaining({
        info: expect.objectContaining({
          title: 'LSIP ETL API',
          version: '1.0',
        }),
      }),
    );

    expect(setupSpy).toHaveBeenCalledWith(
      swaggerDocPath,
      mockApp,
      expect.objectContaining({
        info: expect.objectContaining({
          description: 'With invalid separator',
        }),
      }),
      expect.objectContaining({
        swaggerUrl: swaggerJsonPath,
        customSiteTitle: 'LSIP ETL API Doc',
      }),
    );
  });
});
