import { Body, Controller, HttpCode, HttpStatus, Post, UsePipes, ValidationPipe } from '@nestjs/common';
import { ApiBody, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { VectorDetailService } from './vector-detail.service';
import { UpsertVectorDetailDto } from './dto/upsert-vector-detail.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
import { createSuccessResponse } from '../common/dto/api-response.dto';
import { VectorDetailApiResponseDto } from './dto/vector-detail-response.dto';

@ApiTags('ETL Api')
@Controller('vector-details')
export class VectorDetailController {
  private static readonly validationPipe = new ValidationPipe({ whitelist: true, transform: true });

  constructor(private readonly vectorDetailService: VectorDetailService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @UsePipes(VectorDetailController.validationPipe)
  @ApiOperation({ summary: 'Upsert vector detail records.' })
  @ApiBody({ type: UpsertVectorDetailDto })
  @ApiOkResponse({
    description: 'Vector detail records processed successfully.',
    type: VectorDetailApiResponseDto,
  })
  async upsertVectorDetails(
    @Body() payload: UpsertVectorDetailDto,
  ): Promise<
    ApiResponse<{
      processed: number;
      unprocessedRecords: {
        message: string;
        kpiPerticularAttempted: string | null;
        vectorMasterAttempted: string | null;
      }[];
    }>
  > {
    const result = await this.vectorDetailService.upsertVectorDetails(payload.VectorDetails);

    return createSuccessResponse('Vector detail records processed successfully.', {
      processed: result.processed,
      unprocessedRecords: result.unprocessedRecords,
    });
  }
}
