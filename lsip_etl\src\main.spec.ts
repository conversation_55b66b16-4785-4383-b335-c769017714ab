jest.mock('@nestjs/core', () => ({
  NestFactory: {
    create: jest.fn(),
  },
}));
const setupSwaggerMock = jest.fn();
jest.mock('./config/swagger.config', () => ({
  SwaggerConfig: {
    setup: setupSwaggerMock,
  },
}));

describe('bootstrap', () => {
  const listen = jest.fn();
  let bootstrap: () => Promise<void>;

  const mockApp = {
    listen,
  };

  beforeEach(() => {
    jest.resetModules();
    jest.clearAllMocks();
    setupSwaggerMock.mockReset();
    process.env.NODE_ENV = 'test';

    const { NestFactory } = require('@nestjs/core');
    (NestFactory.create as jest.Mock).mockResolvedValue(mockApp);

    // Import after setting mocks so the module under test picks them up.
    ({ bootstrap } = require('./main'));
  });

  afterEach(() => {
    delete process.env.PORT;
    delete process.env.NODE_ENV;
    jest.restoreAllMocks();
  });

  it('configures Swagger when not running in production', async () => {
    process.env.NODE_ENV = 'development';
    process.env.PORT = '3100';

    await bootstrap();

    expect(setupSwaggerMock).toHaveBeenCalledWith(mockApp);
    expect(listen).toHaveBeenCalledWith(3100);
  });

  it('skips Swagger setup when running in production', async () => {
    process.env.NODE_ENV = 'production';
    process.env.PORT = '3200';

    await bootstrap();

    expect(setupSwaggerMock).not.toHaveBeenCalled();
    expect(listen).toHaveBeenCalledWith(3200);
  });
});
