import fs from "fs";
import { drive_v3, google } from "googleapis";
import XLSX, { WorkBook, WorkSheet } from "xlsx";

const KEYFILEPATH: string = "C:\Users\<USER>\Desktop\project\lsip_etl\internship-poc-473709-2650dce5e12a.json";
const SCOPES: string[] = ["https://www.googleapis.com/auth/drive.readonly"];

const auth = new google.auth.GoogleAuth({
  keyFile: KEYFILEPATH,
  scopes: SCOPES,
});

const drive: drive_v3.Drive = google.drive({ version: "v3", auth });

const FILE_ID: string = "1krtI31BYIf3f5Ei2ATlInsgZjuQIAUiC";

async function downloadExcel() {
  const response = await drive.files.get(
    { fileId: FILE_ID, alt: "media" },
    { responseType: "arraybuffer" }
  );

  const buffer: Buffer = Buffer.from(response.data as ArrayBuffer);
  const workbook: WorkBook = XLSX.read(buffer, { type: "buffer" });

   console.log('Size of Sheets : ', workbook.SheetNames);

    for (let i = 0; i < workbook.SheetNames.length; i++) {

        if(workbook.SheetNames[i] === "Cap table" ) {
          const sheetName: string = workbook.SheetNames[i];
          const sheet: WorkSheet = workbook.Sheets[sheetName];
          
          const jsonData: any = XLSX.utils.sheet_to_json(sheet);
          const jsonString: string = JSON.stringify(jsonData, null, 2);
          
          const fileName: string = workbook.SheetNames[i].trim();
          fs.writeFileSync(`src/ExcelJsonData/${fileName}.json`, jsonString, "utf-8");
        }
    }

  console.log("JSON data has been saved");
}

downloadExcel();