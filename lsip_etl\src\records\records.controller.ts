import { Controller, Get, Query } from '@nestjs/common';
import { ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { createSuccessResponse } from '../common/dto/api-response.dto';
import {
  RecordsSummaryResponseDto,
  SyncedRecordsQueryDto,
  SyncedRecordsResponseDto,
} from './dto/records-summary.dto';
import { RecordsService } from './records.service';

@ApiTags('Records')
@Controller('records')
export class RecordsController {
  constructor(private readonly recordsService: RecordsService) {}

  @Get('summary')
  @ApiOperation({ summary: 'Get per-entity record counts and totals.' })
  @ApiOkResponse({ type: RecordsSummaryResponseDto })
  async getRecordsSummary() {
    const data = await this.recordsService.getRecordsSummary();
    return createSuccessResponse('Record counts aggregated successfully.', data);
  }

  @Get('synced')
  @ApiOperation({
    summary: 'List datasets that have already been synced into the system.',
  })
  @ApiOkResponse({ type: SyncedRecordsResponseDto })
  async getSyncedRecords(@Query() query: SyncedRecordsQueryDto) {
    const includeZero = query.includeZero === 'true';
    const data = await this.recordsService.getSyncedRecords(includeZero);
    return createSuccessResponse('Synced datasets fetched successfully.', data);
  }
}
