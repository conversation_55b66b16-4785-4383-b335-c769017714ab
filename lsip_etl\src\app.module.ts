import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from './config/config.module';
import { DatabaseModule } from './database/database.module';
import { SalesforceModule } from './salesforce/salesforce.module';
import { IndustriesModule } from './industries/industries.module';
import { AccountsModule } from './accounts/accounts.module';
import { CompanyFundsModule } from './company-funds/company-funds.module';
import { ContactsModule } from './contacts/contacts.module';
import { BusinessUnitsModule } from './business-units/business-units.module';
import { BusinessRatingsModule } from './business-ratings/business-ratings.module';
import { FounderRatingsModule } from './founder-ratings/founder-ratings.module';
import { KpiMasterModule } from './kpi-master/kpi-master.module';
import { KpiPerticularModule } from './kpi-perticular/kpi-perticular.module';
import { KpiPlanActualModule } from './kpi-plan-actual/kpi-plan-actual.module';
import { KpiDetailsModule } from './kpi-details/kpi-details.module';
import { KpiRelevancyModule } from './kpi-relevancy/kpi-relevancy.module';
import { VectorMasterModule } from './vector-master/vector-master.module';
import { VectorDetailModule } from './vector-detail/vector-detail.module';
import { VectorPlanActualModule } from './vector-plan-actual/vector-plan-actual.module';
import { RecordsModule } from './records/records.module';

@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    SalesforceModule,
    IndustriesModule,
    AccountsModule,
    CompanyFundsModule,
    ContactsModule,
    BusinessUnitsModule,
    BusinessRatingsModule,
    FounderRatingsModule,
    KpiMasterModule,
    KpiPerticularModule,
    KpiPlanActualModule,
    KpiDetailsModule,
    KpiRelevancyModule,
    VectorMasterModule,
    VectorDetailModule,
    VectorPlanActualModule,
    RecordsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
