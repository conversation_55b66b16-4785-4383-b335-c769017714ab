import { MigrationInterface, QueryRunner } from "typeorm";

export class AddYearFieldToAccount1762165561825 implements MigrationInterface {

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE jvs_accounts ADD COLUMN year__c varchar(255)`);
        await queryRunner.query(`ALTER TABLE jvs_accounts ALTER COLUMN isPartner TYPE BOOLEAN USING (isPartner::BOOLEAN);`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE jvs_accounts DROP COLUMN year__c`);
        await queryRunner.query(` ALTER TABLE jvs_accounts ALTER COLUMN isPartner TYPE VARCHAR(255) USING (isPartner::VARCHAR);`);
    }

}
