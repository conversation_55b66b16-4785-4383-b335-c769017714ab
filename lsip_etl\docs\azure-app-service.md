## Azure App Service (Manual)

This guide wires the NestJS ETL service to the existing Azure App Service infrastructure using the values you provided:

| Variable | Default |
| --- | --- |
| `SUBSCRIPTION` | `LSIP Jarvis` |
| `RESOURCEGROUP` | `appsvc_linux_centralindia_basic` |
| `LOCATION` | `centralindia` |
| `PLANNAME` | `ASP-appsvclinuxcentralindiabasic-9dd2` |
| `PLANSKU` | `P1v2` |
| `SITENAME` | `Jarvisdev` |
| `RUNTIME` | `NODE|22-lts` |
| `STARTUP_FILE` | `npm run start:prod` |

Override any of these by exporting the variable before running the scripts (e.g. `SUBSCRIPTION="LSIP Jarvis" npm run azure:deploy`).

### 0. Prerequisites

1. Azure CLI (`az`), Node.js 22.x, npm 9+, `zip`.
2. Log into Azure once per shell: `az login`.
3. Confirm you are allowed to deploy to the target subscription/resource group.

### 1. Ensure the plan and web app exist

```
npm run azure:bootstrap
```

This script:

- selects the subscription
- creates the resource group (if missing)
- creates or reuses the Linux App Service plan
- creates or updates the Web App with the Node 22 runtime and `npm run start:prod` startup command

### 2. Configure application settings

1. Create your local settings file:

   ```
   cp .azure/appsettings.template.json .azure/appsettings.local.json
   ```

2. Fill in the PostgreSQL values (SF credentials are intentionally excluded for now). The required keys match `src/config/env.validation.ts`.
3. Push the settings to App Service:

   ```
   npm run azure:config -- .azure/appsettings.local.json
   ```

   The script simply calls `az webapp config appsettings set --settings @file`, so you can rerun it any time the app settings need updating.

### 3. Build, package, and deploy

```
npm run azure:deploy
```

The deploy script performs the following steps:

1. `npm ci` (dev deps) and `npm run build`
2. Stages `dist/`, `package*.json`, and a production-only `node_modules` tree under `.azure/build`
3. Produces `.azure/build/app.zip`
4. Executes `az webapp deploy --type zip --clean true --restart true`

The final ZIP is preserved at `.azure/build/app.zip` for auditing or re-use.

### 4. Verify the deployment

Check the site state:

```
az webapp show --resource-group "$RESOURCEGROUP" --name "$SITENAME" --query state -o tsv
```

Tail logs during startup:

```
az webapp log tail --resource-group "$RESOURCEGROUP" --name "$SITENAME"
```

If the container fails to boot, double-check the required environment variables:

```
az webapp config appsettings list --resource-group "$RESOURCEGROUP" --name "$SITENAME" -o table
```

Once this manual path is solid, the same scripts can be invoked from your future CI/CD workflow.
