#!/usr/bin/env bash

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../.." && pwd)"
BUILD_ROOT="${PROJECT_ROOT}/.azure/build"
STAGE_DIR="${BUILD_ROOT}/stage"
ARTIFACT="${BUILD_ROOT}/app.zip"

ENV_FILE="${PROJECT_ROOT}/.azureenv"
if [[ -f "${ENV_FILE}" ]]; then
  set -a
  # shellcheck source=/dev/null
  source "${ENV_FILE}"
  set +a
fi

SUBSCRIPTION="${SUBSCRIPTION:-LSIP Jarvis}"
RESOURCEGROUP="${RESOURCEGROUP:-appsvc_linux_centralindia_basic}"
SITENAME="${SITENAME:-Jarvisdev}"
ENABLE_APP_LOGGING="${ENABLE_APP_LOGGING:-true}"
APP_LOG_LEVEL="${APP_LOG_LEVEL:-information}"
LOG_RETENTION_DAYS="${LOG_RETENTION_DAYS:-7}"
LOG_RETENTION_SUPPORTED=""

function require_cli() {
  if ! command -v "$1" >/dev/null 2>&1; then
    echo "Missing required CLI: $1" >&2
    exit 1
  fi
}

function ensure_login() {
  if ! az account show >/dev/null 2>&1; then
    echo "Azure CLI is not logged in. Run 'az login' first." >&2
    exit 1
  fi
}

function detect_retention_support() {
  if [[ -n "${LOG_RETENTION_SUPPORTED}" ]]; then
    return 0
  fi

  if az webapp log config --help | grep -q -- '--retention-days'; then
    LOG_RETENTION_SUPPORTED="true"
  else
    LOG_RETENTION_SUPPORTED="false"
  fi
}

function configure_logging() {
  local log_args=(
    --name "${SITENAME}"
    --resource-group "${RESOURCEGROUP}"
    --application-logging filesystem
    --level "${APP_LOG_LEVEL}"
    --web-server-logging filesystem
  )

  if [[ -n "${LOG_RETENTION_DAYS:-}" ]]; then
    detect_retention_support
    if [[ "${LOG_RETENTION_SUPPORTED}" == "true" ]]; then
      log_args+=(--retention-days "${LOG_RETENTION_DAYS}")
    else
      echo "Notice: azure-cli build does not support --retention-days; skipping retention configuration" >&2
    fi
  fi

  az webapp log config "${log_args[@]}" >/dev/null
}

function cleanup() {
  rm -rf "${STAGE_DIR}"
}

require_cli az
require_cli npm
require_cli zip
ensure_login
trap cleanup EXIT

echo "Using subscription: ${SUBSCRIPTION}"
az account set --subscription "${SUBSCRIPTION}"

echo "Installing dependencies for build (including dev deps)"
pushd "${PROJECT_ROOT}" >/dev/null
npm ci
echo "Building NestJS project"
npm run build
popd >/dev/null

echo "Preparing production artifact"
rm -rf "${BUILD_ROOT}"
mkdir -p "${STAGE_DIR}"

cp "${PROJECT_ROOT}/package.json" "${STAGE_DIR}/"
cp "${PROJECT_ROOT}/package-lock.json" "${STAGE_DIR}/"
cp -R "${PROJECT_ROOT}/dist" "${STAGE_DIR}/dist"

pushd "${STAGE_DIR}" >/dev/null
echo "Installing production dependencies"
npm ci --omit=dev
echo "Creating deployment zip at ${ARTIFACT}"
zip -r "${ARTIFACT}" . >/dev/null
popd >/dev/null

echo "Deploying ${ARTIFACT} to Web App ${SITENAME}"
az webapp deploy \
  --name "${SITENAME}" \
  --resource-group "${RESOURCEGROUP}" \
  --src-path "${ARTIFACT}" \
  --type zip \
  --restart true \
  --clean true

if [[ "${ENABLE_APP_LOGGING}" == "true" ]]; then
  echo "Re-applying log configuration post-deploy"
  configure_logging
fi

echo "Deployment triggered. Monitor startup logs with:"
echo "  az webapp log tail --name ${SITENAME} --resource-group ${RESOURCEGROUP}"
