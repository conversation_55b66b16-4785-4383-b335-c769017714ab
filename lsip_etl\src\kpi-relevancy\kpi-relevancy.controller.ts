import { Body, Controller, HttpCode, HttpStatus, Post, UsePipes, ValidationPipe } from '@nestjs/common';
import { ApiBody, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { KpiRelevancyService } from './kpi-relevancy.service';
import { UpsertKpiRelevancyDto } from './dto/upsert-kpi-relevancy.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
import { createSuccessResponse } from '../common/dto/api-response.dto';
import { KpiRelevancyApiResponseDto } from './dto/kpi-relevancy-response.dto';

@ApiTags('ETL Api')
@Controller('kpi-relevancies')
export class KpiRelevancyController {
  private static readonly validationPipe = new ValidationPipe({ whitelist: true, transform: true });

  constructor(private readonly kpiRelevancyService: KpiRelevancyService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @UsePipes(KpiRelevancyController.validationPipe)
  @ApiOperation({ summary: 'Upsert KPI relevancy records.' })
  @ApiBody({ type: UpsertKpiRelevancyDto })
  @ApiOkResponse({
    description: 'KPI relevancy records processed successfully.',
    type: KpiRelevancyApiResponseDto,
  })
  async upsertKpiRelevancies(
    @Body() payload: UpsertKpiRelevancyDto,
  ): Promise<
    ApiResponse<{
      processed: number;
      unprocessedRecords: {
        message: string;
        companyAttempted: string | null;
        industryMasterAttempted: string | null;
        kpiMasterAttempted: string | null;
        subIndustryAttempted: string | null;
      }[];
    }>
  > {
    const result = await this.kpiRelevancyService.upsertKpiRelevancies(payload.KpiRelevancies);

    return createSuccessResponse('KPI relevancy records processed successfully.', {
      processed: result.processed,
      unprocessedRecords: result.unprocessedRecords,
    });
  }
}
