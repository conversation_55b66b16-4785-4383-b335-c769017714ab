import { Body, Controller, HttpCode, HttpStatus, Post, UsePipes, ValidationPipe } from '@nestjs/common';
import { ApiBody, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { VectorPlanActualService } from './vector-plan-actual.service';
import { UpsertVectorPlanActualDto } from './dto/upsert-vector-plan-actual.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
import { createSuccessResponse } from '../common/dto/api-response.dto';
import { VectorPlanActualApiResponseDto } from './dto/vector-plan-actual-response.dto';

@ApiTags('ETL Api')
@Controller('vector-plan-actuals')
export class VectorPlanActualController {
  private static readonly validationPipe = new ValidationPipe({ whitelist: true, transform: true });

  constructor(private readonly vectorPlanActualService: VectorPlanActualService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @UsePipes(VectorPlanActualController.validationPipe)
  @ApiOperation({ summary: 'Upsert vector plan actual records.' })
  @ApiBody({ type: UpsertVectorPlanActualDto })
  @ApiOkResponse({
    description: 'Vector plan actual records processed successfully.',
    type: VectorPlanActualApiResponseDto,
  })
  async upsertVectorPlanActuals(
    @Body() payload: UpsertVectorPlanActualDto,
  ): Promise<
    ApiResponse<{
      processed: number;
      unprocessedRecords: {
        message: string;
        vectorDetailAttempted: string | null;
        kpiPlanActualAttempted: string | null;
      }[];
    }>
  > {
    const result = await this.vectorPlanActualService.upsertVectorPlanActuals(payload.VectorPlanActuals);

    return createSuccessResponse('Vector plan actual records processed successfully.', {
      processed: result.processed,
      unprocessedRecords: result.unprocessedRecords,
    });
  }
}
