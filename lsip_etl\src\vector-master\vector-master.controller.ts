import { Body, Controller, HttpCode, HttpStatus, Post, UsePipes, ValidationPipe } from '@nestjs/common';
import { ApiBody, ApiOkResponse, ApiOperation, ApiTags } from '@nestjs/swagger';
import { VectorMasterService } from './vector-master.service';
import { UpsertVectorMasterDto } from './dto/upsert-vector-master.dto';
import type { ApiResponse } from '../common/dto/api-response.dto';
import { createSuccessResponse } from '../common/dto/api-response.dto';
import { VectorMasterApiResponseDto } from './dto/vector-master-response.dto';

@ApiTags('ETL Api')
@Controller('vector-masters')
export class VectorMasterController {
  private static readonly validationPipe = new ValidationPipe({ whitelist: true, transform: true });

  constructor(private readonly vectorMasterService: VectorMasterService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @UsePipes(VectorMasterController.validationPipe)
  @ApiOperation({ summary: 'Upsert vector master records.' })
  @ApiBody({ type: UpsertVectorMasterDto })
  @ApiOkResponse({
    description: 'Vector master records processed successfully.',
    type: VectorMasterApiResponseDto,
  })
  async upsertVectorMasters(
    @Body() payload: UpsertVectorMasterDto,
  ): Promise<
    ApiResponse<{
      processed: number;
      unprocessedRecords: never[];
    }>
  > {
    const result = await this.vectorMasterService.upsertVectorMasters(payload.VectorMasters);

    return createSuccessResponse('Vector master records processed successfully.', {
      processed: result.processed,
      unprocessedRecords: result.unprocessedRecords,
    });
  }
}
