import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBooleanString, IsOptional } from 'class-validator';
import { BaseApiResponseDto } from '../../common/dto/api-response.dto';

export class DatasetSummaryDto {
  @ApiProperty({ example: 'accounts' })
  key!: string;

  @ApiProperty({ example: 'Accounts' })
  label!: string;

  @ApiProperty({ example: 125 })
  count!: number;
}

export class RecordsSummaryDataDto {
  @ApiProperty({ example: 1250 })
  totalRecords!: number;

  @ApiProperty({ type: [DatasetSummaryDto] })
  datasets!: DatasetSummaryDto[];
}

export class RecordsSummaryResponseDto extends BaseApiResponseDto {
  @ApiProperty({ type: RecordsSummaryDataDto })
  declare data: RecordsSummaryDataDto;
}

export class SyncedDatasetDto extends DatasetSummaryDto {
  @ApiProperty({ example: true })
  synced!: boolean;
}

export class SyncedRecordsDataDto {
  @ApiProperty({ example: 15 })
  totalTrackedDatasets!: number;

  @ApiProperty({ example: 12 })
  totalDatasetsWithRecords!: number;

  @ApiProperty({ type: [SyncedDatasetDto] })
  datasets!: SyncedDatasetDto[];
}

export class SyncedRecordsResponseDto extends BaseApiResponseDto {
  @ApiProperty({ type: SyncedRecordsDataDto })
  declare data: SyncedRecordsDataDto;
}

export class SyncedRecordsQueryDto {
  @ApiPropertyOptional({
    description: 'Include datasets that currently have zero records.',
    default: false,
  })
  @IsOptional()
  @IsBooleanString()
  includeZero?: string;
}
